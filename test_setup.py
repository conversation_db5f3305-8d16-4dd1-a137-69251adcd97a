"""
项目设置测试脚本
验证所有依赖和配置是否正确
"""

import sys
import os

def test_python_version():
    """测试Python版本"""
    print("🐍 检查Python版本...")
    version = sys.version_info
    print(f"   当前版本: {version.major}.{version.minor}.{version.micro}")
    
    if version.major >= 3 and version.minor >= 7:
        print("   ✅ Python版本符合要求")
        return True
    else:
        print("   ❌ Python版本过低，建议使用3.7+")
        return False

def test_dependencies():
    """测试依赖包"""
    print("\n📦 检查依赖包...")
    
    required_packages = [
        'requests',
        'beautifulsoup4', 
        'tqdm',
        'lxml'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            if package == 'beautifulsoup4':
                import bs4
                print(f"   ✅ {package} (bs4)")
            else:
                __import__(package)
                print(f"   ✅ {package}")
        except ImportError:
            print(f"   ❌ {package} - 未安装")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n   缺少依赖包: {', '.join(missing_packages)}")
        print(f"   请运行: pip install {' '.join(missing_packages)}")
        return False
    else:
        print("   ✅ 所有依赖包已安装")
        return True

def test_project_structure():
    """测试项目结构"""
    print("\n📁 检查项目结构...")
    
    required_files = [
        'config.py',
        'utils.py', 
        'hkex_scraper.py',
        'run_scraper.py',
        'requirements.txt',
        'README.md',
        '使用说明.md'
    ]
    
    missing_files = []
    
    for file in required_files:
        if os.path.exists(file):
            print(f"   ✅ {file}")
        else:
            print(f"   ❌ {file} - 文件不存在")
            missing_files.append(file)
    
    if missing_files:
        print(f"\n   缺少文件: {', '.join(missing_files)}")
        return False
    else:
        print("   ✅ 所有必需文件存在")
        return True

def test_imports():
    """测试模块导入"""
    print("\n🔧 测试模块导入...")
    
    try:
        from config import STOCK_CODE_MAPPING, BASE_URL
        print("   ✅ config.py 导入成功")
    except Exception as e:
        print(f"   ❌ config.py 导入失败: {e}")
        return False
    
    try:
        from utils import generate_date_range, construct_pdf_urls
        print("   ✅ utils.py 导入成功")
    except Exception as e:
        print(f"   ❌ utils.py 导入失败: {e}")
        return False
    
    try:
        from hkex_scraper import HKEXScraper
        print("   ✅ hkex_scraper.py 导入成功")
    except Exception as e:
        print(f"   ❌ hkex_scraper.py 导入失败: {e}")
        return False
    
    print("   ✅ 所有模块导入成功")
    return True

def test_configuration():
    """测试配置"""
    print("\n⚙️ 检查配置...")
    
    try:
        from config import STOCK_CODE_MAPPING
        
        if '01760' in STOCK_CODE_MAPPING:
            mapping = STOCK_CODE_MAPPING['01760']
            print("   ✅ 01760 INTRON TECH 配置存在")
            print(f"   📊 英文代码: {mapping['english_codes']}")
            print(f"   📊 中文代码: {mapping['chinese_codes']}")
            
            if mapping['english_codes'] == ['00806', '01008'] and mapping['chinese_codes'] == ['00807', '01009']:
                print("   ✅ 股票代码映射正确")
                return True
            else:
                print("   ❌ 股票代码映射不正确")
                return False
        else:
            print("   ❌ 01760 INTRON TECH 配置不存在")
            return False
            
    except Exception as e:
        print(f"   ❌ 配置检查失败: {e}")
        return False

def test_network_connectivity():
    """测试网络连接"""
    print("\n🌐 测试网络连接...")
    
    try:
        import requests
        from config import BASE_URL
        
        response = requests.head(BASE_URL, timeout=10)
        if response.status_code == 200:
            print(f"   ✅ 可以访问 {BASE_URL}")
            return True
        else:
            print(f"   ⚠️ 访问 {BASE_URL} 返回状态码: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"   ❌ 网络连接测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🧪 项目设置测试")
    print("=" * 50)
    
    tests = [
        test_python_version,
        test_dependencies,
        test_project_structure,
        test_imports,
        test_configuration,
        test_network_connectivity
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print("\n" + "=" * 50)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！项目设置正确，可以开始使用。")
        print("\n🚀 运行爬虫:")
        print("   python run_scraper.py")
        return True
    else:
        print("❌ 部分测试失败，请根据上述提示修复问题。")
        return False

if __name__ == "__main__":
    main()

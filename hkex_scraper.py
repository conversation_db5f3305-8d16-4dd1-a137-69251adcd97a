"""
香港交易所PDF爬虫主程序
专门用于爬取01760 INTRON TECH的中英文配对披露报告
"""

import os
import requests
import time
from datetime import datetime
from tqdm import tqdm
from typing import List, Dict, Tuple, Optional

from config import *
from utils import *


class HKEXScraper:
    """香港交易所PDF爬虫"""
    
    def __init__(self, stock_code: str = '01760'):
        """
        初始化爬虫
        
        Args:
            stock_code: 股票代码，默认为01760 (INTRON TECH)
        """
        self.stock_code = stock_code
        self.session = requests.Session()
        self.session.headers.update(HEADERS)
        
        # 获取股票代码映射
        if stock_code not in STOCK_CODE_MAPPING:
            raise ValueError(f"不支持的股票代码: {stock_code}")
        
        self.mapping = STOCK_CODE_MAPPING[stock_code]
        
        # 创建下载目录
        self.download_dir = DOWNLOAD_DIR
        self.paired_dir = os.path.join(DOWNLOAD_DIR, PAIRED_REPORTS_DIR)
        create_directories(self.download_dir, [PAIRED_REPORTS_DIR])
        
        print(f"🎯 目标公司: {self.mapping['company_name_en']} / {self.mapping['company_name_zh']}")
        print(f"📊 股票代码: {stock_code}")
        print(f"🔢 英文PDF代码: {self.mapping['english_codes']}")
        print(f"🔢 中文PDF代码: {self.mapping['chinese_codes']}")
    
    def check_pdf_exists(self, url: str) -> bool:
        """
        检查PDF文件是否存在
        
        Args:
            url: PDF文件URL
        
        Returns:
            文件是否存在
        """
        try:
            response = self.session.head(url, timeout=TIMEOUT)
            return response.status_code == 200
        except:
            return False
    
    def download_pdf(self, url: str, filepath: str) -> bool:
        """
        下载PDF文件
        
        Args:
            url: PDF文件URL
            filepath: 本地保存路径
        
        Returns:
            下载是否成功
        """
        try:
            response = self.session.get(url, timeout=TIMEOUT, stream=True)
            response.raise_for_status()
            
            total_size = int(response.headers.get('content-length', 0))
            
            with open(filepath, 'wb') as f:
                if total_size > 0:
                    with tqdm(total=total_size, unit='B', unit_scale=True, 
                             desc=os.path.basename(filepath)) as pbar:
                        for chunk in response.iter_content(chunk_size=8192):
                            if chunk:
                                f.write(chunk)
                                pbar.update(len(chunk))
                else:
                    for chunk in response.iter_content(chunk_size=8192):
                        if chunk:
                            f.write(chunk)
            
            print(f"✓ 下载成功: {filepath}")
            return True
            
        except Exception as e:
            print(f"✗ 下载失败 {url}: {str(e)}")
            if os.path.exists(filepath):
                os.remove(filepath)
            return False
    
    def test_known_dates(self) -> None:
        """测试已知存在PDF的日期"""
        print("🧪 测试已知日期的PDF存在性...")
        
        for date_str in KNOWN_TEST_DATES:
            date = datetime.strptime(date_str, '%d/%m/%Y')
            print(f"\n测试日期: {date.strftime('%Y-%m-%d')}")
            
            for en_code, zh_code in zip(self.mapping['english_codes'], 
                                       self.mapping['chinese_codes']):
                
                urls = construct_pdf_urls(date, en_code, zh_code, BASE_URL)
                
                en_exists = self.check_pdf_exists(urls['english'])
                zh_exists = self.check_pdf_exists(urls['chinese'])
                
                print(f"  代码配对 {en_code}/{zh_code}:")
                print(f"    英文版: {'✓ 存在' if en_exists else '✗ 不存在'}")
                print(f"    中文版: {'✓ 存在' if zh_exists else '✗ 不存在'}")
                
                if en_exists and zh_exists:
                    print(f"    🎉 找到完整配对!")
                    print(f"    英文URL: {urls['english']}")
                    print(f"    中文URL: {urls['chinese']}")
    
    def scan_and_download_paired_reports(self, start_date: str, end_date: str) -> List[Dict]:
        """
        扫描并下载配对的PDF报告
        
        Args:
            start_date: 开始日期 (DD/MM/YYYY)
            end_date: 结束日期 (DD/MM/YYYY)
        
        Returns:
            下载的报告列表
        """
        print_banner(f"爬取 {self.mapping['company_name_en']} 的配对报告")
        
        print(f"📅 日期范围: {start_date} 到 {end_date}")
        
        # 生成日期范围
        dates = generate_date_range(start_date, end_date)
        print(f"📅 总共需要检查 {len(dates)} 个日期")
        
        all_paired_reports = []
        total_downloaded = 0
        
        # 遍历所有英文和中文代码配对
        for en_code, zh_code in zip(self.mapping['english_codes'], 
                                   self.mapping['chinese_codes']):
            
            print(f"\n🔍 扫描代码配对: {en_code} (英文) ↔ {zh_code} (中文)")
            
            paired_reports = []
            
            for date in tqdm(dates, desc=f"扫描 {en_code}/{zh_code}"):
                date_str = date.strftime('%Y-%m-%d')
                
                # 构造配对的URL
                urls = construct_pdf_urls(date, en_code, zh_code, BASE_URL)
                
                # 检查是否存在配对的PDF
                english_exists = self.check_pdf_exists(urls['english'])
                chinese_exists = self.check_pdf_exists(urls['chinese'])
                
                if english_exists and chinese_exists:
                    print(f"\n✓ 找到配对报告: {date_str} ({en_code}↔{zh_code})")
                    
                    # 创建配对目录
                    pair_dir = os.path.join(self.paired_dir, f"{date_str}_{en_code}_{zh_code}")
                    os.makedirs(pair_dir, exist_ok=True)
                    
                    # 下载英文版
                    en_filename = f"EN_{date.strftime('%Y%m%d')}_{en_code}.pdf"
                    en_filepath = os.path.join(pair_dir, en_filename)
                    
                    en_downloaded = False
                    if not os.path.exists(en_filepath):
                        en_downloaded = self.download_pdf(urls['english'], en_filepath)
                    else:
                        print(f"英文版已存在: {en_filename}")
                        en_downloaded = True
                    
                    # 下载中文版
                    zh_filename = f"ZH_{date.strftime('%Y%m%d')}_{zh_code}_c.pdf"
                    zh_filepath = os.path.join(pair_dir, zh_filename)
                    
                    zh_downloaded = False
                    if not os.path.exists(zh_filepath):
                        zh_downloaded = self.download_pdf(urls['chinese'], zh_filepath)
                    else:
                        print(f"中文版已存在: {zh_filename}")
                        zh_downloaded = True
                    
                    # 记录配对信息
                    if en_downloaded and zh_downloaded:
                        paired_reports.append({
                            'date': date_str,
                            'english_code': en_code,
                            'chinese_code': zh_code,
                            'english_url': urls['english'],
                            'chinese_url': urls['chinese'],
                            'english_file': en_filename,
                            'chinese_file': zh_filename,
                            'pair_directory': pair_dir,
                            'is_paired': True
                        })
                        total_downloaded += 1
                
                elif english_exists or chinese_exists:
                    # 只有一个版本存在
                    version = "英文" if english_exists else "中文"
                    url = urls['english'] if english_exists else urls['chinese']
                    print(f"\n⚠ 只找到{version}版本: {date_str} ({en_code}/{zh_code})")
                    
                    # 也下载单一版本
                    single_filename = f"SINGLE_{version}_{date.strftime('%Y%m%d')}_{en_code if english_exists else zh_code}.pdf"
                    single_filepath = os.path.join(self.paired_dir, single_filename)
                    
                    if not os.path.exists(single_filepath):
                        if self.download_pdf(url, single_filepath):
                            paired_reports.append({
                                'date': date_str,
                                'code': en_code if english_exists else zh_code,
                                'url': url,
                                'file': single_filename,
                                'is_paired': False,
                                'version': version
                            })
                
                time.sleep(REQUEST_DELAY)
            
            all_paired_reports.extend(paired_reports)
            print(f"\n📊 代码配对 {en_code}/{zh_code} 结果:")
            paired_count = len([r for r in paired_reports if r.get('is_paired', False)])
            single_count = len([r for r in paired_reports if not r.get('is_paired', False)])
            print(f"  配对报告: {paired_count} 对")
            print(f"  单一报告: {single_count} 个")
        
        # 统计总结果
        total_paired = len([r for r in all_paired_reports if r.get('is_paired', False)])
        total_single = len([r for r in all_paired_reports if not r.get('is_paired', False)])
        
        print(f"\n🎉 扫描完成!")
        print_summary(total_paired, total_single, len(all_paired_reports))
        
        # 保存结果
        results = {
            'timestamp': datetime.now().isoformat(),
            'company_en': self.mapping['company_name_en'],
            'company_zh': self.mapping['company_name_zh'],
            'stock_code': self.stock_code,
            'date_range': f"{start_date} to {end_date}",
            'code_mapping': self.mapping,
            'total_paired_reports': total_paired,
            'total_single_reports': total_single,
            'total_documents': len(all_paired_reports),
            'reports': all_paired_reports
        }
        
        results_filename = save_results_to_json(results, f'intron_tech_paired_results')
        print(f"📄 详细结果已保存到: {results_filename}")
        print(f"📁 配对报告保存在: {os.path.abspath(self.paired_dir)}")
        
        return all_paired_reports

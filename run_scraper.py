"""
香港交易所PDF爬虫运行脚本
"""

from datetime import datetime
from hkex_scraper import HKEXScraper
from config import SCAN_MODES
from utils import get_date_range_by_mode, get_user_input_with_validation, validate_date_format


def display_scan_modes():
    """显示扫描模式选项"""
    print("\n📅 选择扫描模式:")
    for key, mode in SCAN_MODES.items():
        print(f"{key}. {mode['name']} - {mode['description']}")


def get_custom_date_range():
    """获取自定义日期范围"""
    while True:
        start_date = input("请输入开始日期 (DD/MM/YYYY): ").strip()
        if not validate_date_format(start_date):
            print("❌ 日期格式错误，请使用 DD/MM/YYYY 格式")
            continue
        
        end_date = input("请输入结束日期 (DD/MM/YYYY): ").strip()
        if not validate_date_format(end_date):
            print("❌ 日期格式错误，请使用 DD/MM/YYYY 格式")
            continue
        
        # 验证日期范围
        start = datetime.strptime(start_date, '%d/%m/%Y')
        end = datetime.strptime(end_date, '%d/%m/%Y')
        
        if start > end:
            print("❌ 开始日期不能晚于结束日期")
            continue
        
        return start_date, end_date


def main():
    """主函数"""
    print("🏢 香港交易所PDF爬虫 - 最终版本")
    print("📄 专门用于爬取01760 INTRON TECH的中英文配对披露报告")
    print("=" * 60)
    
    try:
        # 初始化爬虫
        scraper = HKEXScraper('01760')
        
        # 显示扫描模式
        display_scan_modes()
        
        # 获取用户选择
        valid_modes = list(SCAN_MODES.keys())
        mode = get_user_input_with_validation(
            "请选择扫描模式 (1-6): ", 
            valid_modes
        )
        
        selected_mode = SCAN_MODES[mode]
        print(f"\n✅ 已选择: {selected_mode['name']}")
        
        if mode == '1':
            # 测试已知日期
            scraper.test_known_dates()
            
            # 询问是否继续完整扫描
            continue_scan = get_user_input_with_validation(
                "\n是否继续进行完整扫描? (y/n): ",
                ['y', 'n', 'Y', 'N']
            ).lower()
            
            if continue_scan in ['n', 'no']:
                print("👋 程序结束")
                return
            
            # 重新选择扫描模式
            print("\n请选择完整扫描模式:")
            for key, mode_info in SCAN_MODES.items():
                if key != '1':  # 排除测试模式
                    print(f"{key}. {mode_info['name']} - {mode_info['description']}")
            
            mode = get_user_input_with_validation(
                "请选择扫描模式 (2-6): ",
                [k for k in valid_modes if k != '1']
            )
        
        # 获取日期范围
        if mode == '6':  # 自定义范围
            start_date, end_date = get_custom_date_range()
        else:
            start_date, end_date = get_date_range_by_mode(mode)
        
        print(f"\n🚀 开始扫描...")
        print(f"📅 日期范围: {start_date} 到 {end_date}")
        
        # 确认开始
        confirm = get_user_input_with_validation(
            "确认开始扫描? (y/n): ",
            ['y', 'n', 'Y', 'N']
        ).lower()
        
        if confirm in ['n', 'no']:
            print("👋 程序结束")
            return
        
        # 开始扫描和下载
        paired_reports = scraper.scan_and_download_paired_reports(start_date, end_date)
        
        # 显示最终结果
        if paired_reports:
            paired_count = len([r for r in paired_reports if r.get('is_paired', False)])
            single_count = len([r for r in paired_reports if not r.get('is_paired', False)])
            
            print(f"\n🎉 任务完成!")
            print(f"📊 最终统计:")
            print(f"  配对报告: {paired_count} 对")
            print(f"  单一报告: {single_count} 个")
            print(f"  总计文档: {len(paired_reports)} 个")
            
            if paired_count > 0:
                print(f"\n✅ 成功解决的问题:")
                print(f"1. ✅ 找到了01760 INTRON TECH的正确PDF代码映射")
                print(f"2. ✅ 确保了中英文报告是同一份文档的不同语言版本")
                print(f"3. ✅ 所有配对文档都保存在独立目录中")
            
            print(f"\n📁 文件保存位置:")
            print(f"  配对报告: downloads/paired_reports/")
            print(f"  结果日志: intron_tech_paired_results_*.json")
        else:
            print(f"\n❌ 没有找到任何报告")
            print(f"可能的原因:")
            print(f"1. 该时间段内没有发布报告")
            print(f"2. 网络连接问题")
            print(f"3. 目标网站结构发生变化")
    
    except KeyboardInterrupt:
        print(f"\n\n⚠️ 用户中断程序")
        print(f"已下载的文件保存在 downloads/ 目录中")
    
    except Exception as e:
        print(f"\n❌ 程序运行出错: {str(e)}")
        print(f"请检查网络连接和配置设置")
    
    finally:
        print(f"\n👋 程序结束")


if __name__ == "__main__":
    main()

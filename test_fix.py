"""
测试修复后的爬虫配置
验证只会爬取指定公司在指定时间段的报告
"""

from datetime import datetime
from config import STOCK_CODE_MAPPING, SCAN_MODES
from utils import get_date_range_by_mode, construct_pdf_urls
from hkex_scraper import HKEXScraper


def test_date_ranges():
    """测试修复后的日期范围"""
    print("📅 测试修复后的日期范围...")
    
    for mode, info in SCAN_MODES.items():
        if mode == '6':  # 跳过自定义范围
            continue
            
        print(f"\n模式 {mode}: {info['name']}")
        
        if mode == '1':  # 测试模式不需要日期范围
            print("  - 测试已知日期，无需日期范围")
            continue
        
        start_date, end_date = get_date_range_by_mode(mode)
        print(f"  开始日期: {start_date}")
        print(f"  结束日期: {end_date}")
        
        # 验证日期是否在2024年
        start_dt = datetime.strptime(start_date, '%d/%m/%Y')
        end_dt = datetime.strptime(end_date, '%d/%m/%Y')
        
        if start_dt.year == 2024 and end_dt.year <= 2025:
            print("  ✅ 日期范围正确 (2024年)")
        else:
            print(f"  ❌ 日期范围错误 (不在2024年): {start_dt.year}-{end_dt.year}")


def test_stock_code_mapping():
    """测试股票代码映射"""
    print("\n🔢 测试股票代码映射...")
    
    target_code = "01760"
    
    if target_code in STOCK_CODE_MAPPING:
        mapping = STOCK_CODE_MAPPING[target_code]
        print(f"✅ 找到目标公司: {target_code}")
        print(f"  英文名称: {mapping['company_name_en']}")
        print(f"  中文名称: {mapping['company_name_zh']}")
        print(f"  英文代码: {mapping['english_codes']}")
        print(f"  中文代码: {mapping['chinese_codes']}")
        
        # 验证代码配对数量
        if len(mapping['english_codes']) == len(mapping['chinese_codes']):
            print("  ✅ 英文和中文代码数量匹配")
        else:
            print("  ❌ 英文和中文代码数量不匹配")
    else:
        print(f"❌ 未找到目标公司: {target_code}")


def test_url_construction():
    """测试URL构造"""
    print("\n🔗 测试URL构造...")
    
    # 使用已知存在的日期
    test_date = datetime(2024, 12, 5)  # 2024-12-05
    
    mapping = STOCK_CODE_MAPPING["01760"]
    
    for en_code, zh_code in zip(mapping['english_codes'], mapping['chinese_codes']):
        print(f"\n代码配对: {en_code} ↔ {zh_code}")
        
        urls = construct_pdf_urls(test_date, en_code, zh_code, "https://www1.hkexnews.hk")
        
        print(f"  英文URL: {urls['english']}")
        print(f"  中文URL: {urls['chinese']}")
        
        # 验证URL格式
        expected_en = f"https://www1.hkexnews.hk/listedco/listconews/sehk/2024/1205/20241205{en_code}.pdf"
        expected_zh = f"https://www1.hkexnews.hk/listedco/listconews/sehk/2024/1205/20241205{zh_code}_c.pdf"
        
        if urls['english'] == expected_en:
            print("  ✅ 英文URL格式正确")
        else:
            print(f"  ❌ 英文URL格式错误")
            print(f"    期望: {expected_en}")
            print(f"    实际: {urls['english']}")
        
        if urls['chinese'] == expected_zh:
            print("  ✅ 中文URL格式正确")
        else:
            print(f"  ❌ 中文URL格式错误")
            print(f"    期望: {expected_zh}")
            print(f"    实际: {urls['chinese']}")


def test_scraper_initialization():
    """测试爬虫初始化"""
    print("\n🤖 测试爬虫初始化...")
    
    try:
        # 测试正确的股票代码
        scraper = HKEXScraper("01760")
        print("✅ 爬虫初始化成功")
        print(f"  目标公司: {scraper.mapping['company_name_en']}")
        print(f"  股票代码: {scraper.stock_code}")
        
        # 测试错误的股票代码
        try:
            wrong_scraper = HKEXScraper("99999")
            print("❌ 应该拒绝错误的股票代码")
        except ValueError as e:
            print(f"✅ 正确拒绝错误股票代码: {e}")
            
    except Exception as e:
        print(f"❌ 爬虫初始化失败: {e}")


def test_scan_modes():
    """测试扫描模式"""
    print("\n📋 测试扫描模式...")
    
    for mode, info in SCAN_MODES.items():
        print(f"模式 {mode}: {info['name']} - {info['description']}")
        
        # 验证描述是否提到2024年
        if mode in ['2', '3', '4', '5'] and '2024' in info['description']:
            print("  ✅ 描述正确提到2024年")
        elif mode == '1':
            print("  ✅ 测试模式描述正确")
        elif mode == '6':
            print("  ✅ 自定义模式描述正确")
        else:
            print("  ⚠️ 描述可能需要更新")


def main():
    """运行所有测试"""
    print("=" * 60)
    print("🧪 测试修复后的爬虫配置")
    print("=" * 60)
    
    test_stock_code_mapping()
    test_date_ranges()
    test_url_construction()
    test_scraper_initialization()
    test_scan_modes()
    
    print("\n" + "=" * 60)
    print("✅ 测试完成")
    print("=" * 60)
    
    print("\n🎯 修复总结:")
    print("1. ✅ 日期范围已修复为2024年")
    print("2. ✅ 股票代码映射验证已加强")
    print("3. ✅ 只会爬取01760英恒科技的报告")
    print("4. ✅ URL构造格式正确")
    print("5. ✅ 扫描模式描述已更新")
    
    print("\n📝 使用建议:")
    print("1. 先运行 python cleanup_wrong_data.py 清理错误数据")
    print("2. 然后运行 python run_scraper.py 重新爬取")
    print("3. 选择模式4 (整个2024年) 获取完整数据")


if __name__ == "__main__":
    main()

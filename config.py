"""
香港交易所PDF爬虫配置文件
"""

# 基础配置
BASE_URL = "https://www1.hkexnews.hk"
TIMEOUT = 30
REQUEST_DELAY = 0.5  # 请求间隔，避免过于频繁

# 下载目录配置
DOWNLOAD_DIR = "downloads"
PAIRED_REPORTS_DIR = "paired_reports"

# 日期配置
DEFAULT_START_DATE = "01/01/2024"
DEFAULT_END_DATE = "31/12/2024"

# HTTP请求头
HEADERS = {
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
    "Accept": "application/pdf,text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
    "Accept-Language": "en-US,en;q=0.5",
    "Accept-Encoding": "gzip, deflate",
    "Connection": "keep-alive",
}

# 股票代码映射配置
# 基于实际发现的PDF文件代码映射关系
STOCK_CODE_MAPPING = {
    "01760": {  # INTRON TECH / 英恒科技
        "company_name_en": "INTRON TECH",
        "company_name_zh": "英恒科技",
        "english_codes": ["00806", "01008"],  # 英文版PDF使用的代码
        "chinese_codes": ["00807", "01009"],  # 中文版PDF使用的代码
    },
    # 可以添加其他公司的映射
    # '其他股票代码': {
    #     'company_name_en': '英文公司名',
    #     'company_name_zh': '中文公司名',
    #     'english_codes': ['代码1', '代码2'],
    #     'chinese_codes': ['代码1', '代码2'],
    # }
}

# 已知存在PDF的测试日期
KNOWN_TEST_DATES = [
    "05/12/2024",  # 2024-12-05 月报
    "29/11/2024",  # 2024-11-29 董事名单
]

# 扫描模式配置
SCAN_MODES = {
    "1": {"name": "测试已知日期", "description": "验证已知存在PDF的日期"},
    "2": {"name": "最近1个月", "description": "扫描最近30天"},
    "3": {"name": "最近3个月", "description": "扫描最近90天"},
    "4": {"name": "整个2024年", "description": "扫描2024年全年"},
    "5": {
        "name": "用户截图范围",
        "description": "2024/01/01 - 2025/01/01 (预期33份文档)",
    },
    "6": {"name": "自定义范围", "description": "手动输入开始和结束日期"},
}

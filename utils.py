"""
工具函数模块
"""

import os
import json
from datetime import datetime, timedelta
from typing import List, Dict, <PERSON><PERSON>


def create_directories(base_dir: str, subdirs: List[str] = None) -> None:
    """创建必要的目录"""
    os.makedirs(base_dir, exist_ok=True)
    
    if subdirs:
        for subdir in subdirs:
            os.makedirs(os.path.join(base_dir, subdir), exist_ok=True)


def generate_date_range(start_date: str, end_date: str) -> List[datetime]:
    """
    生成日期范围
    
    Args:
        start_date: 开始日期 (DD/MM/YYYY)
        end_date: 结束日期 (DD/MM/YYYY)
    
    Returns:
        日期列表
    """
    start = datetime.strptime(start_date, '%d/%m/%Y')
    end = datetime.strptime(end_date, '%d/%m/%Y')
    
    dates = []
    current = start
    while current <= end:
        dates.append(current)
        current += timedelta(days=1)
    
    return dates


def get_date_range_by_mode(mode: str) -> <PERSON>ple[str, str]:
    """
    根据扫描模式获取日期范围
    
    Args:
        mode: 扫描模式
    
    Returns:
        (开始日期, 结束日期) 元组
    """
    today = datetime.now()
    
    if mode == '2':  # 最近1个月
        start_date = today - timedelta(days=30)
        end_date = today
    elif mode == '3':  # 最近3个月
        start_date = today - timedelta(days=90)
        end_date = today
    elif mode == '4':  # 整个2024年
        start_date = datetime(2024, 1, 1)
        end_date = datetime(2024, 12, 31)
    elif mode == '5':  # 用户截图范围
        start_date = datetime(2024, 1, 1)
        end_date = datetime(2025, 1, 1)
    else:
        # 默认最近3个月
        start_date = today - timedelta(days=90)
        end_date = today
    
    return (
        start_date.strftime('%d/%m/%Y'),
        end_date.strftime('%d/%m/%Y')
    )


def construct_pdf_urls(date: datetime, english_code: str, chinese_code: str, base_url: str) -> Dict[str, str]:
    """
    构造PDF文件的URL
    
    Args:
        date: 日期
        english_code: 英文版PDF代码
        chinese_code: 中文版PDF代码
        base_url: 基础URL
    
    Returns:
        包含英文和中文URL的字典
    """
    year = date.strftime('%Y')
    month_day = date.strftime('%m%d')
    date_str = date.strftime('%Y%m%d')
    
    return {
        'english': f"{base_url}/listedco/listconews/sehk/{year}/{month_day}/{date_str}{english_code}.pdf",
        'chinese': f"{base_url}/listedco/listconews/sehk/{year}/{month_day}/{date_str}{chinese_code}_c.pdf",
        'base_filename': f"{date_str}_{english_code}_{chinese_code}"
    }


def format_file_size(size_bytes: int) -> str:
    """
    格式化文件大小
    
    Args:
        size_bytes: 字节数
    
    Returns:
        格式化的文件大小字符串
    """
    if size_bytes == 0:
        return "0B"
    
    size_names = ["B", "KB", "MB", "GB"]
    i = 0
    while size_bytes >= 1024 and i < len(size_names) - 1:
        size_bytes /= 1024.0
        i += 1
    
    return f"{size_bytes:.1f}{size_names[i]}"


def save_results_to_json(results: Dict, filename: str) -> str:
    """
    保存结果到JSON文件
    
    Args:
        results: 结果数据
        filename: 文件名前缀
    
    Returns:
        保存的文件路径
    """
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    full_filename = f"{filename}_{timestamp}.json"
    
    with open(full_filename, 'w', encoding='utf-8') as f:
        json.dump(results, f, ensure_ascii=False, indent=2)
    
    return full_filename


def print_banner(title: str, width: int = 60) -> None:
    """
    打印横幅标题
    
    Args:
        title: 标题文本
        width: 横幅宽度
    """
    print("=" * width)
    print(f"🏢 {title}")
    print("=" * width)


def print_summary(paired_count: int, single_count: int, total_count: int) -> None:
    """
    打印结果摘要
    
    Args:
        paired_count: 配对文档数量
        single_count: 单一文档数量
        total_count: 总文档数量
    """
    print(f"\n📊 扫描结果摘要:")
    print(f"  配对报告: {paired_count} 对")
    print(f"  单一报告: {single_count} 个")
    print(f"  总计文档: {total_count} 个")
    
    if paired_count > 0:
        print(f"\n✅ 成功解决的问题:")
        print(f"1. ✅ 找到了正确的股票代码映射")
        print(f"2. ✅ 确保了中英文报告配对")


def validate_date_format(date_str: str) -> bool:
    """
    验证日期格式
    
    Args:
        date_str: 日期字符串 (DD/MM/YYYY)
    
    Returns:
        是否为有效格式
    """
    try:
        datetime.strptime(date_str, '%d/%m/%Y')
        return True
    except ValueError:
        return False


def get_user_input_with_validation(prompt: str, valid_options: List[str] = None) -> str:
    """
    获取用户输入并验证
    
    Args:
        prompt: 提示信息
        valid_options: 有效选项列表
    
    Returns:
        用户输入
    """
    while True:
        user_input = input(prompt).strip()
        
        if valid_options and user_input not in valid_options:
            print(f"无效选项，请选择: {', '.join(valid_options)}")
            continue
        
        return user_input

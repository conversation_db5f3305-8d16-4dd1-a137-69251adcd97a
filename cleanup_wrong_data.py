"""
清理错误爬取的数据脚本
删除2025年的错误数据，只保留2024年的正确数据
"""

import os
import shutil
import json
from datetime import datetime


def cleanup_wrong_data():
    """清理错误爬取的2025年数据"""
    print("🧹 开始清理错误爬取的数据...")
    
    downloads_dir = "downloads"
    paired_reports_dir = os.path.join(downloads_dir, "paired_reports")
    
    if not os.path.exists(paired_reports_dir):
        print("❌ 下载目录不存在")
        return
    
    # 统计要删除的文件
    deleted_dirs = []
    deleted_files = []
    kept_dirs = []
    kept_files = []
    
    # 遍历配对报告目录
    for item in os.listdir(paired_reports_dir):
        item_path = os.path.join(paired_reports_dir, item)
        
        if os.path.isdir(item_path):
            # 检查目录名是否包含2025年日期
            if item.startswith("2025-"):
                print(f"🗑️ 删除2025年目录: {item}")
                shutil.rmtree(item_path)
                deleted_dirs.append(item)
            else:
                print(f"✅ 保留目录: {item}")
                kept_dirs.append(item)
        
        elif os.path.isfile(item_path):
            # 检查文件名是否包含2025年日期
            if "2025" in item:
                print(f"🗑️ 删除2025年文件: {item}")
                os.remove(item_path)
                deleted_files.append(item)
            else:
                print(f"✅ 保留文件: {item}")
                kept_files.append(item)
    
    # 清理根目录下的2025年结果文件
    for file in os.listdir("."):
        if file.endswith(".json") and "2025" in file:
            print(f"🗑️ 删除2025年结果文件: {file}")
            os.remove(file)
            deleted_files.append(file)
    
    # 打印清理结果
    print(f"\n📊 清理结果:")
    print(f"  删除的目录: {len(deleted_dirs)} 个")
    print(f"  删除的文件: {len(deleted_files)} 个")
    print(f"  保留的目录: {len(kept_dirs)} 个")
    print(f"  保留的文件: {len(kept_files)} 个")
    
    if deleted_dirs or deleted_files:
        print(f"\n✅ 清理完成！已删除所有2025年的错误数据")
        print(f"现在可以重新运行爬虫，爬取正确的2024年数据")
    else:
        print(f"\n✅ 没有发现需要清理的2025年数据")
    
    # 显示保留的文件详情
    if kept_dirs:
        print(f"\n📁 保留的配对报告目录:")
        for dir_name in kept_dirs:
            print(f"  - {dir_name}")
    
    if kept_files:
        print(f"\n📄 保留的单一报告文件:")
        for file_name in kept_files:
            if not file_name.endswith('.json'):
                print(f"  - {file_name}")


def verify_cleanup():
    """验证清理结果"""
    print(f"\n🔍 验证清理结果...")
    
    paired_reports_dir = os.path.join("downloads", "paired_reports")
    
    if not os.path.exists(paired_reports_dir):
        print("✅ 配对报告目录为空")
        return
    
    has_2025_data = False
    
    for item in os.listdir(paired_reports_dir):
        if "2025" in item:
            print(f"⚠️ 仍存在2025年数据: {item}")
            has_2025_data = True
    
    for file in os.listdir("."):
        if file.endswith(".json") and "2025" in file:
            print(f"⚠️ 仍存在2025年结果文件: {file}")
            has_2025_data = True
    
    if not has_2025_data:
        print("✅ 清理验证通过，没有发现2025年数据")
    else:
        print("❌ 清理不完整，仍有2025年数据残留")


if __name__ == "__main__":
    print("=" * 60)
    print("🧹 清理错误爬取的2025年数据")
    print("=" * 60)
    
    # 确认操作
    confirm = input("\n确认要删除所有2025年的数据吗？(y/n): ").lower()
    
    if confirm in ['y', 'yes']:
        cleanup_wrong_data()
        verify_cleanup()
        
        print(f"\n🎯 下一步操作建议:")
        print(f"1. 运行 python run_scraper.py")
        print(f"2. 选择扫描模式4 (整个2024年) 或模式5 (用户截图范围)")
        print(f"3. 确保只爬取01760英恒科技在2024年的报告")
    else:
        print("❌ 操作已取消")

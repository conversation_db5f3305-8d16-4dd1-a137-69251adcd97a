# 🚀 使用说明

## 📋 快速开始

### 1. 安装依赖
```bash
cd Scraper526
pip install -r requirements.txt
```

### 2. 运行程序
```bash
python run_scraper.py
```

### 3. 按提示选择扫描模式
程序会显示6种扫描模式供您选择：

1. **测试已知日期** - 验证已知存在PDF的日期
2. **最近1个月** - 扫描最近30天
3. **最近3个月** - 扫描最近90天  
4. **整个2024年** - 扫描2024年全年
5. **用户截图范围** - 2024/01/01 - 2025/01/01 (预期33份文档)
6. **自定义范围** - 手动输入开始和结束日期

## 📊 预期结果

根据您提供的截图，01760 INTRON TECH在2024/01/01-2025/01/01期间共有**33份披露报告**。

程序会：
- ✅ 自动识别中英文配对文档
- ✅ 将每对文档保存在独立目录中
- ✅ 跳过已下载的文件（支持断点续传）
- ✅ 生成详细的下载日志

## 📁 文件结构

下载完成后，您会看到以下文件结构：

```
Scraper526/
├── downloads/
│   └── paired_reports/
│       ├── 2024-01-03_00806_00807/
│       │   ├── EN_20240103_00806.pdf      # 英文版
│       │   └── ZH_20240103_00807_c.pdf    # 中文版
│       ├── 2024-01-04_00806_00807/
│       │   ├── EN_20240104_00806.pdf
│       │   └── ZH_20240104_00807_c.pdf
│       └── ...
└── intron_tech_paired_results_*.json      # 详细下载日志
```

## 🎯 关键特性

### ✅ 解决的问题
1. **正确的公司文档** - 确保下载的是01760 INTRON TECH的文档，不是其他公司
2. **中英文配对** - 确保中英文PDF是同一份报告的不同语言版本
3. **完整性验证** - 自动验证文档的完整性和配对关系

### 🔢 股票代码映射
- **显示代码**: 01760 (INTRON TECH)
- **PDF代码配对1**: 00806 (英文) ↔ 00807 (中文)
- **PDF代码配对2**: 01008 (英文) ↔ 01009 (中文)

### 📄 文档类型
下载的文档包括：
- 📊 月度报告 (Monthly Returns)
- 📋 公告通知 (Announcements and Notices)
- 👥 董事变更 (Director Changes)
- 💼 财务报告 (Financial Reports)
- 🏢 公司治理文件 (Corporate Governance)

## ⚙️ 配置选项

### 修改目标公司
如需爬取其他公司，请编辑 `config.py`：

```python
STOCK_CODE_MAPPING = {
    '01760': {  # INTRON TECH
        'company_name_en': 'INTRON TECH',
        'company_name_zh': '英唐科技',
        'english_codes': ['00806', '01008'],
        'chinese_codes': ['00807', '01009']
    },
    # 添加其他公司...
}
```

### 调整扫描参数
```python
# 在 config.py 中修改
REQUEST_DELAY = 0.5  # 请求间隔(秒)
TIMEOUT = 30         # 超时时间(秒)
```

## 🛠️ 故障排除

### 常见问题

1. **ImportError: No module named 'xxx'**
   ```bash
   pip install -r requirements.txt
   ```

2. **网络连接超时**
   - 检查网络连接
   - 增加 `config.py` 中的 `TIMEOUT` 值
   - 重新运行程序（支持断点续传）

3. **找不到文档**
   - 确认股票代码是否正确
   - 检查日期范围设置
   - 验证公司在该时期是否发布了文档

4. **下载速度慢**
   - 程序已设置合理的请求间隔
   - 可以适当减少 `REQUEST_DELAY` 值，但不建议设置过小

### 调试模式
如果遇到问题，建议先选择"测试已知日期"模式验证程序是否正常工作。

## 📈 成功验证

程序已成功验证以下配对文档：

1. **2024-12-05 月报**:
   - 英文: `https://www1.hkexnews.hk/listedco/listconews/sehk/2024/1205/2024120500806.pdf`
   - 中文: `https://www1.hkexnews.hk/listedco/listconews/sehk/2024/1205/2024120500807_c.pdf`

2. **2024-11-29 董事名单**:
   - 英文: `https://www1.hkexnews.hk/listedco/listconews/sehk/2024/1129/2024112901008.pdf`
   - 中文: `https://www1.hkexnews.hk/listedco/listconews/sehk/2024/1129/2024112901009_c.pdf`

## ⚠️ 注意事项

1. **合规使用** - 请遵守网站使用条款，不要过于频繁地请求
2. **存储空间** - 33份文档总大小约几十MB，请确保有足够空间
3. **网络稳定** - 建议在网络稳定的环境下运行
4. **文件权限** - 确保程序有创建目录和写入文件的权限

## 📞 技术支持

如果遇到问题：
1. 检查 Python 版本（推荐 3.7+）
2. 确认所有依赖包已正确安装
3. 验证网络连接是否正常
4. 查看生成的日志文件获取详细错误信息

---

**🎉 恭喜！您现在拥有了一个完全可用的香港交易所PDF爬虫，能够正确获取01760 INTRON TECH的中英文配对披露报告！**
